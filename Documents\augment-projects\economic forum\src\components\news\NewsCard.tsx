'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Clock, User, ArrowRight } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'

interface Article {
  id: string
  title: string
  excerpt?: string
  author: string
  date: string
  category: string
  readTime: string
  slug?: string
  image?: string
}

export default function NewsCard({ article }: { article: Article }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="bg-white rounded-lg shadow-lg overflow-hidden"
    >
      <div className="relative h-48">
        {article.image ? (
          <img
            src={article.image}
            alt={article.title}
            className="object-cover w-full h-full"
          />
        ) : (
          <div className="w-full h-full bg-gradient-ghana opacity-10" />
        )}
      </div>
      <div className="p-6">
        <div className="flex justify-between items-center mb-4">
          <span className="inline-block px-3 py-1 rounded-full text-sm font-medium bg-ghana-yellow/10 text-ghana-yellow">
            {article.category}
          </span>
          <div className="flex items-center text-gray-500 text-sm">
            <Clock className="w-4 h-4 mr-1" />
            {article.readTime}
          </div>
        </div>
        <h3 className="text-xl font-bold mb-3 line-clamp-2">{article.title}</h3>
        <p className="text-gray-600 mb-4 line-clamp-3">{article.excerpt}</p>
        <div className="flex justify-between items-center">
          <div className="flex items-center text-sm text-gray-500">
            <User className="w-4 h-4 mr-1" />
            <span>{article.author}</span>
            <span className="mx-2">•</span>
            <span>{article.date}</span>
          </div>
          <Link
            href={`/news/${article.id}`}
            className="inline-flex items-center text-ghana-green hover:text-ghana-yellow transition-colors duration-200"
          >
            Read More <ArrowRight className="w-4 h-4 ml-2" />
          </Link>
        </div>
      </div>
    </motion.div>
  )
}
