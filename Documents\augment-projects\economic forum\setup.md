# African Economic Forum - Setup Guide

## Quick Start

Follow these steps to get the African Economic Forum website running locally:

### 1. Prerequisites

Make sure you have the following installed:
- **Node.js** (version 18 or higher)
- **npm**, **yarn**, or **pnpm**
- **Git**

### 2. Installation

```bash
# Navigate to the project directory
cd "Documents\augment-projects\economic forum"

# Install dependencies
npm install
# or
yarn install
# or
pnpm install
```

### 3. Environment Setup

```bash
# Copy the environment example file
cp .env.example .env.local

# Edit the .env.local file with your configuration
# (Optional for basic development)
```

### 4. Run Development Server

**Option A: Quick Start (Windows)**
```bash
# Double-click the batch file or run in Command Prompt
run-local.bat
```

**Option B: PowerShell**
```powershell
# Run in PowerShell
.\run-local.ps1
```

**Option C: Manual Commands**
```bash
# Start on default port (3000)
npm run dev

# Start on port 5000
npm run dev:5000

# Or with yarn/pnpm
yarn dev:5000
pnpm dev:5000
```

### 5. Open in Browser

Navigate to [http://localhost:5000](http://localhost:5000) to see the website.
(Or [http://localhost:3000](http://localhost:3000) if using default port)

## Project Structure

```
african-economic-forum/
├── src/
│   ├── app/                 # Next.js 14 app directory
│   │   ├── globals.css     # Global styles
│   │   ├── layout.tsx      # Root layout
│   │   ├── page.tsx        # Home page
│   │   ├── about/          # About page
│   │   ├── events/         # Events page
│   │   ├── speakers/       # Speakers page
│   │   ├── news/           # News page
│   │   └── contact/        # Contact page
│   ├── components/         # Reusable components
│   │   ├── home/          # Home page components
│   │   └── layout/        # Layout components
│   └── lib/               # Utilities and configurations
├── public/                # Static assets
├── package.json          # Dependencies and scripts
├── tailwind.config.ts    # Tailwind CSS configuration
├── tsconfig.json         # TypeScript configuration
└── next.config.js        # Next.js configuration
```

## Features Implemented

✅ **Home Page**
- Hero section with animations
- Mission statement
- Animated statistics
- Upcoming events preview
- Featured news section
- Interactive Africa map

✅ **Layout Components**
- Responsive header with glassmorphism
- Footer with Ghana wave animation
- Mobile-friendly navigation

✅ **Design System**
- Ghana flag color palette
- Custom animations with Framer Motion
- Glassmorphism effects
- Responsive typography

✅ **Basic Pages**
- About Us (placeholder)
- Events (placeholder)
- Speakers (placeholder)
- News & Insights (placeholder)
- Contact (placeholder)

## Next Steps

### Phase 1: Content Development
- [ ] Complete About Us page with leadership team
- [ ] Build Events system with registration
- [ ] Create Speaker profiles and bios
- [ ] Implement News/Blog CMS

### Phase 2: Backend Integration
- [ ] Set up database (PostgreSQL/Supabase)
- [ ] Implement authentication (NextAuth.js)
- [ ] Create admin dashboard
- [ ] Add contact form functionality

### Phase 3: Advanced Features
- [ ] Search functionality
- [ ] Newsletter integration
- [ ] Payment processing (if needed)
- [ ] Multi-language support

### Phase 4: Optimization
- [ ] SEO optimization
- [ ] Performance improvements
- [ ] Accessibility enhancements
- [ ] Testing implementation

## Development Commands

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint

# Testing (when implemented)
npm run test         # Run unit tests
npm run test:watch   # Run tests in watch mode
npm run test:e2e     # Run end-to-end tests
```

## Deployment Options

### Vercel (Recommended)
1. Push code to GitHub
2. Connect repository to Vercel
3. Configure environment variables
4. Deploy automatically

### Other Platforms
- **Netlify**: Configure for Next.js
- **Heroku**: Use Next.js buildpack
- **AWS Amplify**: Deploy with AWS

## Troubleshooting

### Common Issues

**Node.js not found:**
- Install Node.js from [nodejs.org](https://nodejs.org)
- Restart your terminal/command prompt

**Dependencies not installing:**
- Clear npm cache: `npm cache clean --force`
- Delete `node_modules` and `package-lock.json`
- Run `npm install` again

**Port 3000 already in use:**
- Use a different port: `npm run dev -- -p 3001`
- Or kill the process using port 3000

**Build errors:**
- Check TypeScript errors: `npm run lint`
- Ensure all imports are correct
- Verify environment variables

## Support

For questions or issues:
- Check the README.md file
- Review the project documentation
- Create an issue in the repository
- Contact the development team

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

---

**Happy coding! 🚀**
