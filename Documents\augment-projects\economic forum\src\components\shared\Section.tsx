'use client'

import React from 'react'
import { motion } from 'framer-motion'

interface SectionProps {
  id?: string
  title: string
  subtitle?: string
  children: React.ReactNode
  className?: string
  background?: 'white' | 'gray' | 'gradient'
}

export default function Section({ 
  id, 
  title, 
  subtitle, 
  children, 
  className = '',
  background = 'white'
}: SectionProps) {
  const bgClasses = {
    white: 'bg-white',
    gray: 'bg-gray-50',
    gradient: 'bg-gradient-ghana text-white'
  }

  return (
    <section id={id} className={`py-20 ${bgClasses[background]} ${className}`}>
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className={`text-4xl font-bold font-serif mb-4 ${background === 'gradient' ? 'text-white' : 'text-gray-900'}`}>
            {title}
          </h2>
          {subtitle && (
            <p className={`text-xl ${background === 'gradient' ? 'text-white opacity-90' : 'text-gray-700'}`}>
              {subtitle}
            </p>
          )}
        </motion.div>
        {children}
      </div>
    </section>
  )
}
