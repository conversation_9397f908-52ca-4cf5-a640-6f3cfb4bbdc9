# African Economic Forum Website

A modern, full-stack website for the African Economic Forum built with Next.js 14, featuring Ghana-themed design, animations, and comprehensive content management.

## 🚀 Features

- **Modern Tech Stack**: Next.js 14, TypeScript, TailwindCSS, Framer Motion
- **Ghana-Themed Design**: Custom color palette inspired by Ghana's flag
- **Responsive Design**: Mobile-first approach with elegant animations
- **Glassmorphism UI**: Modern transparent cards and navigation
- **SEO Optimized**: Server-side rendering and meta tag optimization
- **Performance**: Image optimization and lazy loading
- **Accessibility**: ARIA labels and keyboard navigation support

## 🎨 Design System

### Colors
- **Ghana Red**: `#CE1126` - Primary accent color
- **Ghana Yellow**: `#FCD116` - Secondary accent color  
- **Ghana Green**: `#007847` - Success and growth color
- **Ghana Black**: `#000000` - Text and contrast color

### Typography
- **Primary**: Inter (sans-serif)
- **Headings**: Playfair Display (serif)
- **Code**: JetBrains Mono (monospace)

## 📁 Project Structure

```
src/
├── app/                    # Next.js 14 app directory
│   ├── globals.css        # Global styles and Tailwind imports
│   ├── layout.tsx         # Root layout component
│   └── page.tsx           # Home page
├── components/            # Reusable components
│   ├── home/             # Home page specific components
│   │   ├── Hero.tsx
│   │   ├── MissionStatement.tsx
│   │   ├── StatsSection.tsx
│   │   ├── UpcomingEvents.tsx
│   │   ├── FeaturedNews.tsx
│   │   └── AfricaMap.tsx
│   └── layout/           # Layout components
│       ├── Header.tsx
│       └── Footer.tsx
└── lib/                  # Utility functions and configurations
    └── utils.ts
```

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd african-economic-forum
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```
   Fill in your environment variables in `.env.local`

4. **Run the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🔧 Configuration

### TailwindCSS
The project uses a custom Tailwind configuration with Ghana-themed colors and animations. See `tailwind.config.ts` for the complete configuration.

### Framer Motion
Animations are implemented using Framer Motion with custom variants for consistent motion design throughout the application.

## 📱 Pages

### Implemented
- **Home Page**: Hero section, mission statement, stats, events preview, news, Africa map

### Planned
- **About Us**: Mission, leadership team, partners
- **Events**: Event listings, details, registration
- **Speakers**: Speaker profiles and bios
- **News & Insights**: Blog/news system with CMS
- **Contact**: Contact form and information

## 🎯 Key Components

### Hero Section
- Animated background with floating elements
- Ghana flag color gradients
- Call-to-action buttons with hover effects
- Animated statistics counter

### Mission Statement
- Two-column layout with values grid
- Hover animations and transitions
- Ghana-themed color accents

### Stats Section
- Animated counters with easing functions
- Glassmorphism cards
- Background patterns and floating elements

### Events Preview
- Featured event with large card layout
- Grid of upcoming events
- Category badges and metadata

### News Section
- Featured article with large image
- Sidebar with recent articles
- Category filtering and search

### Africa Map
- Interactive regional markers
- Hover tooltips with statistics
- Key initiatives showcase

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Configure environment variables
4. Deploy automatically

### Other Platforms
- **Netlify**: Configure build settings for Next.js
- **Heroku**: Use the Next.js buildpack
- **AWS**: Deploy using AWS Amplify or EC2

## 🧪 Testing

```bash
# Run unit tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run E2E tests
npm run test:e2e
```

## 📈 Performance

- **Lighthouse Score**: Optimized for 90+ scores
- **Image Optimization**: Next.js built-in optimization
- **Code Splitting**: Automatic route-based splitting
- **Lazy Loading**: Components and images load on demand

## 🔒 Security

- **HTTPS**: Enforced in production
- **CSP Headers**: Content Security Policy configured
- **Environment Variables**: Sensitive data in environment files
- **Input Validation**: Form validation and sanitization

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Ghana flag colors and cultural inspiration
- African economic development organizations
- Open source community and contributors

## 📞 Support

For support and questions:
- Email: <EMAIL>
- Website: [African Economic Forum](https://africaneconomicforum.org)
- GitHub Issues: [Create an issue](https://github.com/your-repo/issues)
