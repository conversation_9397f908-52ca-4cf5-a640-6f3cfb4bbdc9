'use client'

import { motion } from 'framer-motion'
import { Target, Users, Globe, TrendingUp } from 'lucide-react'
import Link from 'next/link'

const values = [
  {
    icon: Target,
    title: 'Strategic Vision',
    description: 'Developing comprehensive strategies for sustainable economic growth across Africa.',
  },
  {
    icon: Users,
    title: 'Collaborative Leadership',
    description: 'Bringing together diverse stakeholders to create meaningful partnerships.',
  },
  {
    icon: Globe,
    title: 'Continental Unity',
    description: 'Fostering unity and cooperation among African nations for shared prosperity.',
  },
  {
    icon: TrendingUp,
    title: 'Innovation Focus',
    description: 'Promoting innovation and technology as drivers of economic transformation.',
  },
]

export default function MissionStatement() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Left Column - Mission Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="mb-6">
              <span className="inline-block px-4 py-2 bg-ghana-yellow/10 text-ghana-red font-semibold rounded-full text-sm mb-4">
                Our Mission
              </span>
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 font-serif">
                Empowering Africa's 
                <span className="text-ghana-green"> Economic Future</span>
              </h2>
            </div>
            
            <div className="space-y-6 text-lg text-gray-600 leading-relaxed">
              <p>
                The African Economic Forum serves as a catalyst for transformative economic 
                development across the continent. We unite visionary leaders, innovative 
                entrepreneurs, and forward-thinking policymakers to create sustainable 
                solutions for Africa's most pressing economic challenges.
              </p>
              
              <p>
                Through strategic partnerships, knowledge sharing, and collaborative 
                initiatives, we're building a foundation for inclusive growth that 
                benefits all African communities while positioning the continent as 
                a global economic powerhouse.
              </p>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              className="mt-8"
            >
              <Link
                href="/about"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-ghana-red to-ghana-green text-white font-semibold rounded-lg hover:shadow-lg transition-all duration-300 group"
              >
                Learn More About Us
                <motion.span
                  className="ml-2"
                  animate={{ x: [0, 5, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  →
                </motion.span>
              </Link>
            </motion.div>
          </motion.div>

          {/* Right Column - Values Grid */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="grid grid-cols-1 sm:grid-cols-2 gap-6"
          >
            {values.map((value, index) => {
              const Icon = value.icon
              return (
                <motion.div
                  key={value.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ y: -5 }}
                  className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 group"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-ghana-yellow to-ghana-green rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {value.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {value.description}
                  </p>
                </motion.div>
              )
            })}
          </motion.div>
        </div>

        {/* Call to Action Section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-20 text-center"
        >
          <div className="bg-gradient-to-r from-ghana-red via-ghana-yellow to-ghana-green p-1 rounded-2xl max-w-4xl mx-auto">
            <div className="bg-white rounded-xl p-8 md:p-12">
              <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 font-serif">
                Ready to Shape Africa's Economic Future?
              </h3>
              <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
                Join thousands of leaders, innovators, and changemakers who are 
                driving economic transformation across the continent.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link
                    href="/join"
                    className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-ghana-red to-ghana-green text-white font-semibold rounded-full hover:shadow-lg transition-all duration-300"
                  >
                    Become a Member
                  </Link>
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link
                    href="/events"
                    className="inline-flex items-center px-8 py-4 border-2 border-ghana-green text-ghana-green font-semibold rounded-full hover:bg-ghana-green hover:text-white transition-all duration-300"
                  >
                    Attend Our Events
                  </Link>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
