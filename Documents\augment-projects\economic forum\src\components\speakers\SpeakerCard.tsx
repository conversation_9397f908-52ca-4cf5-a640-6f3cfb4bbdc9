'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Linkedin, Twitter, Globe, Youtube, Award, Users, Calendar } from 'lucide-react'
import Link from 'next/link'

export interface Speaker {
  id: string
  name: string
  role: string
  organization: string
  bio: string
  topics: string[]
  expertise: string[]
  achievements: string[]
  upcomingEvents?: Array<{
    name: string
    date: string
    link: string
  }>
  pastEvents?: number
  image?: string
  videoUrl?: string
  social?: {
    linkedin?: string
    twitter?: string
    website?: string
    youtube?: string
  }
}

interface SpeakerCardProps {
  speaker: Speaker
}

export default function SpeakerCard({ speaker }: SpeakerCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="bg-white rounded-lg shadow-lg overflow-hidden"
    >
      <div className="relative">
        <div className="aspect-w-3 aspect-h-4">
          {speaker.image ? (
            <img
              src={speaker.image}
              alt={speaker.name}
              className="object-cover w-full h-full"
            />
          ) : (
            <div className="w-full h-full bg-gradient-ghana opacity-10" />
          )}
        </div>
        {speaker.videoUrl && (
          <Link
            href={speaker.videoUrl}
            className="absolute bottom-4 right-4 bg-ghana-red text-white p-2 rounded-full hover:bg-ghana-yellow transition-colors duration-200"
            title="Watch speaker video"
          >
            <Youtube className="w-5 h-5" />
          </Link>
        )}
      </div>

      <div className="p-6">
        <h3 className="text-xl font-bold mb-2">{speaker.name}</h3>
        <p className="text-ghana-green font-medium">{speaker.role}</p>
        <p className="text-gray-600 mb-4">{speaker.organization}</p>
        
        <p className="text-gray-700 mb-6">{speaker.bio}</p>
        
        <div className="space-y-6">
          {/* Expertise */}
          <div>
            <h4 className="text-sm font-semibold text-gray-900 mb-2 flex items-center">
              <Award className="w-4 h-4 mr-2 text-ghana-green" />
              Areas of Expertise:
            </h4>
            <div className="flex flex-wrap gap-2">
              {speaker.topics.map((topic, index) => (
                <span
                  key={index}
                  className="inline-block px-3 py-1 rounded-full text-sm 
                           bg-ghana-yellow/10 text-ghana-yellow"
                >
                  {topic}
                </span>
              ))}
            </div>
          </div>

          {/* Key Achievements */}
          <div>
            <h4 className="text-sm font-semibold text-gray-900 mb-2 flex items-center">
              <Users className="w-4 h-4 mr-2 text-ghana-green" />
              Key Achievements:
            </h4>
            <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
              {speaker.achievements.map((achievement, index) => (
                <li key={index}>{achievement}</li>
              ))}
            </ul>
          </div>

          {/* Upcoming Events */}
          {speaker.upcomingEvents && speaker.upcomingEvents.length > 0 && (
            <div>
              <h4 className="text-sm font-semibold text-gray-900 mb-2 flex items-center">
                <Calendar className="w-4 h-4 mr-2 text-ghana-green" />
                Upcoming Appearances:
              </h4>
              <div className="space-y-2">
                {speaker.upcomingEvents.map((event, index) => (
                  <Link
                    key={index}
                    href={event.link}
                    className="block text-sm hover:bg-gray-50 p-2 rounded"
                  >
                    <span className="font-medium text-ghana-green">{event.name}</span>
                    <span className="text-gray-600 ml-2">({event.date})</span>
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Social Links */}
        {speaker.social && (
          <div className="flex space-x-4 mt-6 pt-6 border-t border-gray-200">
            {Object.entries(speaker.social).map(([platform, url]) => (
              <a
                key={platform}
                href={url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-ghana-green transition-colors"
              >
                {platform === 'linkedin' && <Linkedin className="w-5 h-5" />}
                {platform === 'twitter' && <Twitter className="w-5 h-5" />}
                {platform === 'website' && <Globe className="w-5 h-5" />}
                {platform === 'youtube' && <Youtube className="w-5 h-5" />}
              </a>
            ))}
          </div>
        )}
      </div>
    </motion.div>
  )
}
