'use client'

import type { Metadata } from 'next'
import { useState } from 'react'
import NewsCard from '@/components/news/NewsCard'
import { Search, Filter } from 'lucide-react'

export const metadata: Metadata = {
  title: 'News & Insights',
  description: 'Stay updated with the latest news, insights, and analysis on African economic development.',
}

const categories = [
  'All',
  'Economy',
  'Policy',
  'Technology',
  'Investment',
  'Development',
  'Trade'
]

// Mock data for development - replace with actual API call
const mockArticles = [
  {
    id: '1',
    title: 'Digital Transformation: Africa\'s Path to Economic Growth',
    excerpt: 'How digital technologies are reshaping African economies and creating new opportunities for growth and development.',
    author: 'Dr<PERSON><PERSON>',
    date: 'Mar 15, 2024',
    category: 'Technology',
    readTime: '5 min',
    slug: 'digital-transformation-africa',
    image: '/images/news/digital-transformation.jpg'
  },
  {
    id: '2',
    title: 'Sustainable Development Goals: Africa\'s Progress Report',
    excerpt: 'A comprehensive analysis of Africa\'s advancement towards achieving the UN Sustainable Development Goals.',
    author: '<PERSON>',
    date: 'Mar 12, 2024',
    category: 'Development',
    readTime: '7 min',
    slug: 'sdg-progress-report',
    image: '/images/news/sustainable-development.jpg'
  },
  {
    id: '3',
    title: 'AfCFTA: One Year Later',
    excerpt: 'Evaluating the impact of the African Continental Free Trade Area one year after implementation.',
    author: 'John Okonjo',
    date: 'Mar 10, 2024',
    category: 'Trade',
    readTime: '6 min',
    slug: 'afcfta-one-year-later',
    image: '/images/news/trade-agreement.jpg'
  }
]

export default function NewsPage() {
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [searchQuery, setSearchQuery] = useState('')

  const filteredArticles = mockArticles.filter(article => {
    const matchesCategory = selectedCategory === 'All' || article.category === selectedCategory
    const matchesSearch = article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (article.excerpt && article.excerpt.toLowerCase().includes(searchQuery.toLowerCase()))
    return matchesCategory && matchesSearch
  })

  return (
    <div className="min-h-screen pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 font-serif">
            News & 
            <span className="text-ghana-green"> Insights</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Stay informed with the latest developments, analysis, and insights 
            shaping Africa's economic landscape.
          </p>
        </div>

        <div className="mt-12 space-y-8">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search articles..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ghana-green focus:border-transparent"
              />
            </div>
            <div className="flex items-center gap-2 overflow-x-auto pb-2 w-full md:w-auto">
              <Filter className="text-ghana-green h-5 w-5 flex-shrink-0" />
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-colors duration-200
                    ${selectedCategory === category
                      ? 'bg-ghana-green text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredArticles.length > 0 ? (
              filteredArticles.map((article) => (
                <NewsCard key={article.id} article={article} />
              ))
            ) : (
              <div className="col-span-full text-center py-12">
                <p className="text-gray-600">No articles found matching your criteria.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
