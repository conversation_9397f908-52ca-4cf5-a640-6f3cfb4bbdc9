'use client'

import { motion } from 'framer-motion'
import { Calendar, MapPin, Users, ArrowRight } from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'

const upcomingEvents = [
  {
    id: 1,
    title: 'Africa Investment Summit 2024',
    date: 'March 15-17, 2024',
    location: 'Accra, Ghana',
    attendees: '500+ Leaders',
    description: 'Connecting global investors with African opportunities across key sectors including technology, infrastructure, and renewable energy.',
    image: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    category: 'Investment',
    featured: true,
  },
  {
    id: 2,
    title: 'Digital Africa Conference',
    date: 'April 22-24, 2024',
    location: 'Lagos, Nigeria',
    attendees: '300+ Innovators',
    description: 'Exploring the digital transformation of African economies and the role of technology in driving sustainable growth.',
    image: 'https://images.unsplash.com/photo-1559136555-9303baea8ebd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    category: 'Technology',
    featured: false,
  },
  {
    id: 3,
    title: 'Sustainable Energy Forum',
    date: 'May 10-12, 2024',
    location: 'Cape Town, South Africa',
    attendees: '400+ Experts',
    description: 'Advancing renewable energy solutions and sustainable development across the African continent.',
    image: 'https://images.unsplash.com/photo-1466611653911-95081537e5b7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    category: 'Energy',
    featured: false,
  },
]

export default function UpcomingEvents() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <span className="inline-block px-4 py-2 bg-ghana-green/10 text-ghana-green font-semibold rounded-full text-sm mb-4">
            Upcoming Events
          </span>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 font-serif">
            Join Africa's Premier 
            <span className="text-ghana-red"> Economic Gatherings</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Connect with industry leaders, discover new opportunities, and be part of 
            the conversations shaping Africa's economic future.
          </p>
        </motion.div>

        {/* Featured Event */}
        {upcomingEvents.filter(event => event.featured).map((event, index) => (
          <motion.div
            key={event.id}
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="mb-16"
          >
            <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
              <div className="grid grid-cols-1 lg:grid-cols-2">
                {/* Image */}
                <div className="relative h-64 lg:h-auto">
                  <Image
                    src={event.image}
                    alt={event.title}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="bg-ghana-red text-white px-3 py-1 rounded-full text-sm font-medium">
                      Featured Event
                    </span>
                  </div>
                </div>

                {/* Content */}
                <div className="p-8 lg:p-12 flex flex-col justify-center">
                  <div className="mb-4">
                    <span className="inline-block px-3 py-1 bg-ghana-yellow/20 text-ghana-red font-medium rounded-full text-sm mb-4">
                      {event.category}
                    </span>
                    <h3 className="text-3xl font-bold text-gray-900 mb-4 font-serif">
                      {event.title}
                    </h3>
                  </div>

                  <p className="text-gray-600 mb-6 leading-relaxed">
                    {event.description}
                  </p>

                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8">
                    <div className="flex items-center space-x-2 text-gray-600">
                      <Calendar className="w-5 h-5 text-ghana-green" />
                      <span className="text-sm">{event.date}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-gray-600">
                      <MapPin className="w-5 h-5 text-ghana-green" />
                      <span className="text-sm">{event.location}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-gray-600">
                      <Users className="w-5 h-5 text-ghana-green" />
                      <span className="text-sm">{event.attendees}</span>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-4">
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Link
                        href={`/events/${event.id}`}
                        className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-ghana-red to-ghana-green text-white font-semibold rounded-lg hover:shadow-lg transition-all duration-300 group"
                      >
                        Register Now
                        <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                      </Link>
                    </motion.div>
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Link
                        href={`/events/${event.id}`}
                        className="inline-flex items-center px-6 py-3 border-2 border-ghana-green text-ghana-green font-semibold rounded-lg hover:bg-ghana-green hover:text-white transition-all duration-300"
                      >
                        Learn More
                      </Link>
                    </motion.div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        ))}

        {/* Other Events Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          {upcomingEvents.filter(event => !event.featured).map((event, index) => (
            <motion.div
              key={event.id}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
              className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group"
            >
              <div className="relative h-48">
                <Image
                  src={event.image}
                  alt={event.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-4 left-4">
                  <span className="bg-ghana-yellow text-ghana-red px-3 py-1 rounded-full text-sm font-medium">
                    {event.category}
                  </span>
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-ghana-red transition-colors duration-300">
                  {event.title}
                </h3>
                <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                  {event.description}
                </p>

                <div className="space-y-2 mb-6">
                  <div className="flex items-center space-x-2 text-gray-600">
                    <Calendar className="w-4 h-4 text-ghana-green" />
                    <span className="text-sm">{event.date}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-600">
                    <MapPin className="w-4 h-4 text-ghana-green" />
                    <span className="text-sm">{event.location}</span>
                  </div>
                </div>

                <Link
                  href={`/events/${event.id}`}
                  className="inline-flex items-center text-ghana-red font-semibold hover:text-ghana-green transition-colors duration-300 group"
                >
                  Learn More
                  <ArrowRight className="ml-1 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                </Link>
              </div>
            </motion.div>
          ))}
        </div>

        {/* View All Events CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <Link
            href="/events"
            className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-ghana-green to-ghana-yellow text-white font-semibold rounded-full hover:shadow-lg transition-all duration-300 group"
          >
            View All Events
            <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
          </Link>
        </motion.div>
      </div>
    </section>
  )
}
