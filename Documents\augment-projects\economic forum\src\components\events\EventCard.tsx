'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Calendar, Clock, MapPin, Users, ArrowRight } from 'lucide-react'
import Link from 'next/link'

export interface Event {
  id: string
  title: string
  date: string
  time: string
  location: string
  type: 'conference' | 'workshop' | 'summit' | 'roundtable'
  capacity: number
  description: string
  image?: string
  registrationUrl: string
  speakers: Array<{
    name: string
    role: string
  }>
}

export default function EventCard({ event }: { event: Event }) {
  const typeColors = {
    conference: 'bg-ghana-red/10 text-ghana-red',
    workshop: 'bg-ghana-yellow/10 text-ghana-yellow',
    summit: 'bg-ghana-green/10 text-ghana-green',
    roundtable: 'bg-blue-100 text-blue-600'
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="bg-white rounded-lg shadow-lg overflow-hidden"
    >
      <div className="relative h-48">
        {event.image ? (
          <img
            src={event.image}
            alt={event.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full bg-gradient-ghana opacity-10" />
        )}
        <span className={`absolute top-4 right-4 px-3 py-1 rounded-full text-sm font-medium ${typeColors[event.type]}`}>
          {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
        </span>
      </div>

      <div className="p-6">
        <h3 className="text-xl font-bold mb-4">{event.title}</h3>
        
        <div className="space-y-3 mb-6">
          <div className="flex items-center text-gray-600">
            <Calendar className="w-5 h-5 mr-3 text-ghana-green" />
            <span>{event.date}</span>
          </div>
          <div className="flex items-center text-gray-600">
            <Clock className="w-5 h-5 mr-3 text-ghana-green" />
            <span>{event.time}</span>
          </div>
          <div className="flex items-center text-gray-600">
            <MapPin className="w-5 h-5 mr-3 text-ghana-green" />
            <span>{event.location}</span>
          </div>
          <div className="flex items-center text-gray-600">
            <Users className="w-5 h-5 mr-3 text-ghana-green" />
            <span>Capacity: {event.capacity} attendees</span>
          </div>
        </div>

        <p className="text-gray-600 mb-6 line-clamp-3">{event.description}</p>

        {event.speakers.length > 0 && (
          <div className="mb-6">
            <h4 className="font-semibold mb-2 text-gray-900">Featured Speakers:</h4>
            <div className="space-y-2">
              {event.speakers.map((speaker, index) => (
                <div key={index} className="text-sm text-gray-600">
                  <span className="font-medium">{speaker.name}</span>
                  <span className="mx-2">•</span>
                  <span>{speaker.role}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="flex justify-between items-center">
          <Link
            href={event.registrationUrl}
            className="inline-flex items-center text-ghana-green hover:text-ghana-yellow transition-colors duration-200"
          >
            Register Now <ArrowRight className="w-4 h-4 ml-2" />
          </Link>
        </div>
      </div>
    </motion.div>
  )
}
