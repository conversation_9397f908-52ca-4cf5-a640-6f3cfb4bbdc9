'use client'

import React from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { 
  Facebook, 
  Twitter, 
  Linkedin,
  Instagram, 
  Youtube,
  Mail,
  Phone,
  MapPin,
  LucideIcon
} from 'lucide-react'

interface SocialLink {
  name: string;
  icon: LucideIcon;
  href: string;
}

const footerLinks = {
  about: [
    { name: 'Our Mission', href: '/about#mission' },
    { name: 'Leadership Team', href: '/about#leadership' },
    { name: 'Partners', href: '/about#partners' },
    { name: 'Annual Reports', href: '/reports' },
  ],
  events: [
    { name: 'Upcoming Events', href: '/events' },
    { name: 'Past Events', href: '/events/past' },
    { name: 'Event Calendar', href: '/events/calendar' },
    { name: 'Speakers Bureau', href: '/speakers' },
  ],
  resources: [
    { name: 'News & Insights', href: '/news' },
    { name: 'Research Papers', href: '/research' },
    { name: 'Policy Briefs', href: '/policy' },
    { name: 'Media Kit', href: '/media' },
  ],
  connect: [
    { name: 'Contact Us', href: '/contact' },
    { name: 'Join Forum', href: '/join' },
    { name: 'Newsletter', href: '/newsletter' },
    { name: 'Careers', href: '/careers' },
  ],
}

const socialLinks: SocialLink[] = [
  { name: 'Facebook', icon: Facebook, href: '#' },
  { name: 'Twitter', icon: Twitter, href: '#' },
  { name: 'LinkedIn', icon: Linkedin, href: '#' },
  { name: 'Instagram', icon: Instagram, href: '#' },
  { name: 'Youtube', icon: Youtube, href: '#' },
]

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white relative overflow-hidden">
      {/* Ghana Wave Animation */}
      <div className="absolute top-0 left-0 right-0 h-2 ghana-wave"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-8">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-12">
          {/* Logo and Description */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <Link href="/" className="flex items-center space-x-2 mb-4">
                <div className="w-12 h-12 bg-gradient-ghana rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-xl">AEF</span>
                </div>
                <span className="text-xl font-bold">African Economic Forum</span>
              </Link>
              <p className="text-gray-300 mb-6 max-w-md">
                Driving economic growth and development across Africa through collaboration, 
                innovation, and sustainable partnerships. Building a prosperous future for all Africans.
              </p>
              
              {/* Contact Info */}
              <div className="space-y-2 text-sm text-gray-300">
                <div className="flex items-center space-x-2">
                  <MapPin className="w-4 h-4 text-ghana-yellow" />
                  <span>Accra, Ghana</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Phone className="w-4 h-4 text-ghana-yellow" />
                  <span>+233 (0) 123 456 789</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="w-4 h-4 text-ghana-yellow" />
                  <span><EMAIL></span>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Footer Links */}
          {Object.entries(footerLinks).map(([category, links], index) => (
            <motion.div
              key={category}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <h3 className="text-lg font-semibold mb-4 capitalize text-ghana-yellow">
                {category}
              </h3>
              <ul className="space-y-2">
                {links.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-gray-300 hover:text-ghana-yellow transition-colors duration-200 text-sm"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>

        {/* Newsletter Signup */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="border-t border-gray-700 pt-8 mb-8"
        >
          <div className="max-w-md">
            <h3 className="text-lg font-semibold mb-2 text-ghana-yellow">
              Stay Updated
            </h3>
            <p className="text-gray-300 text-sm mb-4">
              Get the latest news and updates from the African Economic Forum.
            </p>
            <div className="flex">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-2 bg-gray-800 border border-gray-600 rounded-l-lg focus:outline-none focus:border-ghana-yellow text-white"
              />
              <button className="bg-gradient-to-r from-ghana-red to-ghana-green text-white px-6 py-2 rounded-r-lg hover:shadow-lg transition-all duration-300">
                Subscribe
              </button>
            </div>
          </div>
        </motion.div>

        {/* Bottom Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="border-t border-gray-700 pt-8 flex flex-col md:flex-row justify-between items-center"
        >
          <div className="text-gray-300 text-sm mb-4 md:mb-0">
            © 2024 African Economic Forum. All rights reserved.
          </div>
          
          {/* Social Links */}
          <div className="flex space-x-4">
            {socialLinks.map((social) => (
              <motion.a
                key={social.name}
                href={social.href}
                whileHover={{ scale: 1.1, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center text-gray-300 hover:text-ghana-yellow hover:bg-gray-700 transition-all duration-300"
                aria-label={social.name}
              >
                {React.createElement(social.icon, { className: "w-5 h-5" })}
              </motion.a>
            ))}
          </div>
        </motion.div>

        {/* Legal Links */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="mt-4 pt-4 border-t border-gray-700 text-center"
        >
          <div className="flex flex-wrap justify-center space-x-6 text-sm text-gray-400">
            <Link href="/privacy" className="hover:text-ghana-yellow transition-colors duration-200">
              Privacy Policy
            </Link>
            <Link href="/terms" className="hover:text-ghana-yellow transition-colors duration-200">
              Terms of Service
            </Link>
            <Link href="/cookies" className="hover:text-ghana-yellow transition-colors duration-200">
              Cookie Policy
            </Link>
            <Link href="/accessibility" className="hover:text-ghana-yellow transition-colors duration-200">
              Accessibility
            </Link>
          </div>
        </motion.div>
      </div>
    </footer>
  )
}
