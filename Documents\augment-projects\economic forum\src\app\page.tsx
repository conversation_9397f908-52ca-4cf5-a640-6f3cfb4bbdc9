'use client'

import { motion } from 'framer-motion'
import Hero from '@/components/home/<USER>'
import MissionStatement from '@/components/home/<USER>'
import StatsSection from '@/components/home/<USER>'
import UpcomingEvents from '@/components/home/<USER>'
import FeaturedNews from '@/components/home/<USER>'
import AfricaMap from '@/components/home/<USER>'

const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6, ease: "easeOut" }
}

const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
}

export default function Home() {
  return (
    <motion.div
      variants={staggerContainer}
      initial="initial"
      animate="animate"
      className="overflow-hidden"
    >
      {/* Hero Section */}
      <motion.section variants={fadeInUp}>
        <Hero />
      </motion.section>

      {/* Mission Statement */}
      <motion.section variants={fadeInUp} className="py-20 bg-gray-50">
        <MissionStatement />
      </motion.section>

      {/* Stats Section */}
      <motion.section variants={fadeInUp} className="py-20 bg-gradient-ghana">
        <StatsSection />
      </motion.section>

      {/* Africa Map/Timeline */}
      <motion.section variants={fadeInUp} className="py-20 bg-white">
        <AfricaMap />
      </motion.section>

      {/* Upcoming Events */}
      <motion.section variants={fadeInUp} className="py-20 bg-gray-50">
        <UpcomingEvents />
      </motion.section>

      {/* Featured News */}
      <motion.section variants={fadeInUp} className="py-20 bg-white">
        <FeaturedNews />
      </motion.section>
    </motion.div>
  )
}
