'use client'

import React from 'react'
import { motion } from 'framer-motion'

interface PageHeroProps {
  title: string
  subtitle?: string
  backgroundImage?: string
}

export default function PageHero({ title, subtitle, backgroundImage }: PageHeroProps) {
  return (
    <section className={`relative h-[50vh] bg-gradient-ghana overflow-hidden ${backgroundImage ? 'bg-cover bg-center' : ''}`}
             style={backgroundImage ? { backgroundImage: `url(${backgroundImage})` } : undefined}>
      {backgroundImage && <div className="absolute inset-0 bg-black opacity-50" />}
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="container mx-auto px-4 h-full flex items-center justify-center text-white text-center relative z-10"
      >
        <div className="max-w-3xl">
          <h1 className="text-5xl md:text-6xl font-bold font-serif mb-6">
            {title}
          </h1>
          {subtitle && (
            <p className="text-xl opacity-90">
              {subtitle}
            </p>
          )}
        </div>
      </motion.div>
    </section>
  )
}
