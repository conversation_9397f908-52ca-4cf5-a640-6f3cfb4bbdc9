{"name": "african-economic-forum", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:5000": "next dev -p 5000", "build": "next build", "start": "next start", "start:5000": "next start -p 5000", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:e2e": "playwright test"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@next/font": "14.0.4", "@prisma/client": "^5.7.1", "@supabase/supabase-js": "^2.38.5", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "next": "^15.3.3", "next-auth": "^4.24.5", "postcss": "^8.4.32", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.48.2", "tailwind-merge": "^2.2.0", "tailwindcss": "^3.3.6", "zod": "^3.22.4"}, "devDependencies": {"@playwright/test": "^1.40.1", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@types/jest": "^29.5.8", "@types/node": "^20", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "eslint": "^8", "eslint-config-next": "14.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prisma": "^5.7.1", "typescript": "^5"}}