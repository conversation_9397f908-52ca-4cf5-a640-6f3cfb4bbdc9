'use client'

import { motion } from 'framer-motion'
import { Calendar, ArrowRight, Clock } from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'

const featuredArticles = [
  {
    id: 1,
    title: 'Africa\'s Digital Economy Reaches $180 Billion Milestone',
    excerpt: 'New research reveals unprecedented growth in Africa\'s digital sector, with fintech and e-commerce leading the transformation across the continent.',
    author: 'Dr. <PERSON><PERSON>',
    date: '2024-01-15',
    readTime: '5 min read',
    category: 'Digital Economy',
    image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    featured: true,
  },
  {
    id: 2,
    title: 'Green Energy Investments Surge 300% Across Sub-Saharan Africa',
    excerpt: 'Renewable energy projects attract record funding as African nations accelerate their transition to sustainable power sources.',
    author: 'Prof. <PERSON><PERSON>',
    date: '2024-01-12',
    readTime: '4 min read',
    category: 'Renewable Energy',
    image: 'https://images.unsplash.com/photo-1509391366360-2e959784a276?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    featured: false,
  },
  {
    id: 3,
    title: 'AfCFTA Trade Volume Exceeds $50 Billion in First Quarter',
    excerpt: 'The African Continental Free Trade Area demonstrates strong momentum with significant increases in intra-African trade.',
    author: 'Sarah Okafor',
    date: '2024-01-10',
    readTime: '6 min read',
    category: 'Trade & Commerce',
    image: 'https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    featured: false,
  },
  {
    id: 4,
    title: 'Youth Entrepreneurship Programs Launch in 15 African Cities',
    excerpt: 'New initiative aims to support young entrepreneurs with funding, mentorship, and market access across major African urban centers.',
    author: 'Michael Banda',
    date: '2024-01-08',
    readTime: '3 min read',
    category: 'Entrepreneurship',
    image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
    featured: false,
  },
]

function formatDate(dateString: string) {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  })
}

export default function FeaturedNews() {
  const featuredArticle = featuredArticles.find(article => article.featured)
  const otherArticles = featuredArticles.filter(article => !article.featured)

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <span className="inline-block px-4 py-2 bg-ghana-red/10 text-ghana-red font-semibold rounded-full text-sm mb-4">
            Latest Insights
          </span>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 font-serif">
            News & 
            <span className="text-ghana-green"> Economic Insights</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Stay informed with the latest developments, analysis, and insights 
            shaping Africa's economic landscape.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Featured Article */}
          {featuredArticle && (
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="lg:col-span-2"
            >
              <article className="bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 group">
                <div className="relative h-64 md:h-80">
                  <Image
                    src={featuredArticle.image}
                    alt={featuredArticle.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="bg-ghana-red text-white px-3 py-1 rounded-full text-sm font-medium">
                      Featured
                    </span>
                  </div>
                  <div className="absolute bottom-4 left-4">
                    <span className="bg-ghana-yellow text-ghana-red px-3 py-1 rounded-full text-sm font-medium">
                      {featuredArticle.category}
                    </span>
                  </div>
                </div>

                <div className="p-8">
                  <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>{formatDate(featuredArticle.date)}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>{featuredArticle.readTime}</span>
                    </div>
                  </div>

                  <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4 group-hover:text-ghana-red transition-colors duration-300 font-serif">
                    {featuredArticle.title}
                  </h3>

                  <p className="text-gray-600 mb-6 leading-relaxed">
                    {featuredArticle.excerpt}
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-500">
                      By <span className="font-medium text-gray-700">{featuredArticle.author}</span>
                    </div>
                    <Link
                      href={`/news/${featuredArticle.id}`}
                      className="inline-flex items-center text-ghana-red font-semibold hover:text-ghana-green transition-colors duration-300 group"
                    >
                      Read More
                      <ArrowRight className="ml-1 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                    </Link>
                  </div>
                </div>
              </article>
            </motion.div>
          )}

          {/* Other Articles */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            {otherArticles.map((article, index) => (
              <motion.article
                key={article.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-gray-50 rounded-xl p-6 hover:bg-white hover:shadow-lg transition-all duration-300 group"
              >
                <div className="flex space-x-4">
                  <div className="relative w-20 h-20 flex-shrink-0">
                    <Image
                      src={article.image}
                      alt={article.title}
                      fill
                      className="object-cover rounded-lg group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="mb-2">
                      <span className="inline-block px-2 py-1 bg-ghana-green/10 text-ghana-green text-xs font-medium rounded">
                        {article.category}
                      </span>
                    </div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-ghana-red transition-colors duration-300 line-clamp-2">
                      {article.title}
                    </h4>
                    <div className="flex items-center space-x-3 text-xs text-gray-500 mb-2">
                      <span>{formatDate(article.date)}</span>
                      <span>•</span>
                      <span>{article.readTime}</span>
                    </div>
                    <Link
                      href={`/news/${article.id}`}
                      className="inline-flex items-center text-sm text-ghana-red font-medium hover:text-ghana-green transition-colors duration-300 group"
                    >
                      Read More
                      <ArrowRight className="ml-1 w-3 h-3 group-hover:translate-x-1 transition-transform duration-300" />
                    </Link>
                  </div>
                </div>
              </motion.article>
            ))}

            {/* View All News CTA */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="pt-4"
            >
              <Link
                href="/news"
                className="block w-full text-center py-4 bg-gradient-to-r from-ghana-red to-ghana-green text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300"
              >
                View All News & Insights
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
