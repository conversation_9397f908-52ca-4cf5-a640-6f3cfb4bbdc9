import type { Metadata } from 'next'
import { Inter, Playfair_Display } from 'next/font/google'
import './globals.css'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
})

const playfair = Playfair_Display({ 
  subsets: ['latin'],
  variable: '--font-playfair',
  display: 'swap',
})

export const metadata: Metadata = {
  title: {
    default: 'African Economic Forum',
    template: '%s | African Economic Forum'
  },
  description: 'Driving economic growth and development across Africa through collaboration, innovation, and sustainable partnerships.',
  keywords: ['Africa', 'Economic Forum', 'Development', 'Ghana', 'Business', 'Investment', 'Trade'],
  authors: [{ name: 'African Economic Forum' }],
  creator: 'African Economic Forum',
  publisher: 'African Economic Forum',
  metadataBase: new URL('https://african-economic-forum.com'),
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://african-economic-forum.com',
    title: 'African Economic Forum',
    description: 'Driving economic growth and development across Africa through collaboration, innovation, and sustainable partnerships.',
    siteName: 'African Economic Forum',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'African Economic Forum',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'African Economic Forum',
    description: 'Driving economic growth and development across Africa through collaboration, innovation, and sustainable partnerships.',
    images: ['/og-image.jpg'],
    creator: '@AfricanEconForum',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${inter.variable} ${playfair.variable}`}>
      <body className={`${inter.className} antialiased`}>
        <div className="min-h-screen flex flex-col">
          <Header />
          <main className="flex-grow">
            {children}
          </main>
          <Footer />
        </div>
      </body>
    </html>
  )
}
