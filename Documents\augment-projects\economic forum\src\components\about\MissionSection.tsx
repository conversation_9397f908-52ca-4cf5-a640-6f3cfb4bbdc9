'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Target, Lightbulb, Users, Globe } from 'lucide-react'

export default function MissionSection() {
  const values = [
    {
      icon: Target,
      title: 'Pan-African Vision',
      description: 'Driving economic transformation across all regions of Africa through collaborative initiatives and sustainable development programs.'
    },
    {
      icon: Lightbulb,
      title: 'Innovation',
      description: 'Fostering technological advancement and innovative solutions to address Africa's unique economic challenges.'
    },
    {
      icon: Users,
      title: 'Collaboration',
      description: 'Building strong partnerships between governments, businesses, and academic institutions to achieve shared prosperity.'
    },
    {
      icon: Globe,
      title: 'Sustainability',
      description: 'Promoting environmentally conscious and socially responsible economic development practices.'
    }
  ]

  const initiatives = [
    {
      title: 'Economic Research',
      description: 'Conducting comprehensive research on African markets and economic trends.',
      stats: [
        { value: '50+', label: 'Research Papers' },
        { value: '30', label: 'Countries Covered' },
        { value: '100+', label: 'Contributors' }
      ]
    },
    {
      title: 'Policy Advocacy',
      description: 'Working with governments to implement effective economic policies.',
      stats: [
        { value: '25+', label: 'Policy Briefs' },
        { value: '15', label: 'Governments' },
        { value: '40+', label: 'Partners' }
      ]
    },
    {
      title: 'Capacity Building',
      description: 'Training and development programs for economic leaders.',
      stats: [
        { value: '1000+', label: 'Participants' },
        { value: '20', label: 'Programs' },
        { value: '35', label: 'Partners' }
      ]
    }
  ]

  return (
    <div className="space-y-20">
      {/* Core Values */}
      <div>
        <h2 className="text-3xl font-bold text-center mb-12">Our Core Values</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {values.map((value, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="mb-4 flex justify-center">
                <value.icon className="w-12 h-12 text-ghana-green" />
              </div>
              <h3 className="text-xl font-bold mb-3">{value.title}</h3>
              <p className="text-gray-600">{value.description}</p>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Key Initiatives */}
      <div>
        <h2 className="text-3xl font-bold text-center mb-12">Key Initiatives</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {initiatives.map((initiative, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-white rounded-lg shadow-lg p-6"
            >
              <h3 className="text-xl font-bold mb-3">{initiative.title}</h3>
              <p className="text-gray-600 mb-6">{initiative.description}</p>
              <div className="grid grid-cols-3 gap-4">
                {initiative.stats.map((stat, statIndex) => (
                  <div key={statIndex} className="text-center">
                    <div className="text-2xl font-bold text-ghana-green mb-1">
                      {stat.value}
                    </div>
                    <div className="text-sm text-gray-500">{stat.label}</div>
                  </div>
                ))}
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Impact Statement */}
      <div className="bg-gradient-ghana text-white rounded-lg p-8 md:p-12">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-6">Our Impact</h2>
          <p className="text-lg mb-8">
            Since our inception, we have been at the forefront of driving economic 
            transformation across Africa. Through our initiatives, we have:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              { value: '$500M+', label: 'Investment Facilitated' },
              { value: '10,000+', label: 'Leaders Trained' },
              { value: '45', label: 'Countries Impacted' }
            ].map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl font-bold mb-2">{stat.value}</div>
                <div className="text-sm opacity-90">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
