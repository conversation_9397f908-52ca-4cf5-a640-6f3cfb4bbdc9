'use client'

import React from 'react'
import { motion } from 'framer-motion'
import Image from 'next/image'

interface TeamMember {
  name: string
  role: string
  bio: string
  image?: string
  social?: {
    linkedin?: string
    twitter?: string
  }
}

interface Leadership {
  boardOfDirectors: TeamMember[]
  executiveTeam: TeamMember[]
  advisoryBoard: TeamMember[]
}

export default function LeadershipSection() {
  const leadership: Leadership = {
    boardOfDirectors: [
      {
        name: 'Dr. <PERSON>',
        role: 'Chairman of the Board',
        bio: 'Former World Bank economist with over 25 years of experience in African development.',
        social: {
          linkedin: '#',
          twitter: '#'
        }
      },
      {
        name: 'Mrs. <PERSON><PERSON>',
        role: 'Vice Chair',
        bio: 'Leading expert in sustainable economic policies and founder of West African Investment Group.',
        social: {
          linkedin: '#'
        }
      }
    ],
    executiveTeam: [
      {
        name: 'Mr. <PERSON>',
        role: 'Executive Director',
        bio: 'Seasoned business leader with expertise in pan-African operations and policy implementation.',
        social: {
          linkedin: '#',
          twitter: '#'
        }
      },
      {
        name: 'Dr. <PERSON><PERSON>',
        role: 'Director of Research',
        bio: 'PhD in Economics from LSE with focus on African monetary policy and regional integration.',
        social: {
          twitter: '#'
        }
      }
    ],
    advisoryBoard: [
      {
        name: 'Prof. <PERSON>',
        role: 'Senior Advisor',
        bio: 'Professor of Economics at University of Ghana and consultant to African Development Bank.',
        social: {
          linkedin: '#'
        }
      }
    ]
  }

  const renderTeamMember = (member: TeamMember) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="bg-white rounded-lg shadow-lg overflow-hidden"
    >
      <div className="aspect-w-3 aspect-h-4 relative">
        {member.image ? (
          <Image
            src={member.image}
            alt={member.name}
            fill
            className="object-cover"
          />
        ) : (
          <div className="w-full h-full bg-gradient-ghana opacity-10" />
        )}
      </div>
      <div className="p-6">
        <h3 className="text-xl font-bold mb-2">{member.name}</h3>
        <p className="text-ghana-green font-medium mb-3">{member.role}</p>
        <p className="text-gray-600 mb-4">{member.bio}</p>
      </div>
    </motion.div>
  )

  return (
    <>
      {/* Board of Directors */}
      <div className="mb-16">
        <h2 className="text-3xl font-bold text-center mb-8">Board of Directors</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {leadership.boardOfDirectors.map((member, index) => (
            <div key={index}>{renderTeamMember(member)}</div>
          ))}
        </div>
      </div>

      {/* Executive Team */}
      <div className="mb-16">
        <h2 className="text-3xl font-bold text-center mb-8">Executive Team</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {leadership.executiveTeam.map((member, index) => (
            <div key={index}>{renderTeamMember(member)}</div>
          ))}
        </div>
      </div>

      {/* Advisory Board */}
      <div>
        <h2 className="text-3xl font-bold text-center mb-8">Advisory Board</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {leadership.advisoryBoard.map((member, index) => (
            <div key={index}>{renderTeamMember(member)}</div>
          ))}
        </div>
      </div>
    </>
  )
}
