# 🚀 Quick Start - African Economic Forum

## Run on localhost:5000 in 3 Steps

### Step 1: Prerequisites
Make sure you have **Node.js** installed:
- Download from [nodejs.org](https://nodejs.org) (LTS version recommended)
- Verify installation: Open Command Prompt and type `node --version`

### Step 2: Install Dependencies
Open Command Prompt or PowerShell in the project folder and run:
```bash
npm install
```

### Step 3: Start the Server
Choose one of these options:

**🎯 Easiest Way (Windows):**
- Double-click `run-local.bat` file
- Or run in Command Prompt: `run-local.bat`

**💻 PowerShell:**
```powershell
.\run-local.ps1
```

**⌨️ Manual Command:**
```bash
npm run dev:5000
```

### Step 4: Open Browser
Navigate to: **http://localhost:5000**

---

## 🎉 What You'll See

✅ **Hero Section** - Animated Ghana-themed landing with statistics
✅ **Mission Statement** - Values and call-to-action
✅ **Stats Section** - Animated counters on Ghana gradient background  
✅ **Africa Map** - Interactive regional markers
✅ **Events Preview** - Featured and upcoming events
✅ **News Section** - Latest insights and articles
✅ **Responsive Design** - Works on mobile, tablet, desktop

---

## 🛠️ Development Commands

```bash
npm run dev:5000      # Start on port 5000
npm run dev           # Start on port 3000 (default)
npm run build         # Build for production
npm run start:5000    # Start production server on port 5000
npm run lint          # Check code quality
```

---

## 🎨 Features Included

- **Ghana Flag Colors** - Red (#CE1126), Yellow (#FCD116), Green (#007847)
- **Glassmorphism UI** - Transparent cards and navigation
- **Framer Motion** - Smooth animations throughout
- **Responsive Design** - Mobile-first approach
- **SEO Optimized** - Meta tags and structured data
- **Performance** - Image optimization and lazy loading

---

## 📁 Key Files

```
src/
├── app/
│   ├── page.tsx           # Home page
│   ├── layout.tsx         # Root layout
│   └── globals.css        # Global styles
├── components/
│   ├── home/              # Home page components
│   │   ├── Hero.tsx
│   │   ├── MissionStatement.tsx
│   │   ├── StatsSection.tsx
│   │   ├── UpcomingEvents.tsx
│   │   ├── FeaturedNews.tsx
│   │   └── AfricaMap.tsx
│   └── layout/
│       ├── Header.tsx     # Navigation
│       └── Footer.tsx     # Footer with Ghana wave
└── lib/
    └── utils.ts           # Utility functions
```

---

## 🔧 Troubleshooting

**Port 5000 already in use?**
```bash
npm run dev           # Use port 3000 instead
# or
npm run dev -- -p 3001   # Use port 3001
```

**Dependencies not installing?**
```bash
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

**Build errors?**
```bash
npm run lint          # Check for errors
```

---

## 🌟 Next Steps

1. **Customize Content** - Edit components in `src/components/home/<USER>
2. **Add Database** - Integrate Supabase or PostgreSQL
3. **Add CMS** - Connect Sanity.io or Strapi for content management
4. **Deploy** - Use Vercel, Netlify, or Heroku

---

**🎯 Ready to build Africa's economic future? Start the server and explore!**
