'use client'

import React from 'react'
import { motion } from 'framer-motion'

interface Partner {
  name: string
  category: 'strategic' | 'academic' | 'corporate' | 'government'
  description: string
  logo?: string
  website?: string
}

export default function PartnersSection() {
  const partners: Partner[] = [
    {
      name: 'African Development Bank',
      category: 'strategic',
      description: 'Key partner in economic development initiatives and funding programs.',
      website: 'https://www.afdb.org'
    },
    {
      name: 'United Nations Economic Commission for Africa',
      category: 'strategic',
      description: 'Collaboration on policy research and economic analysis.',
      website: 'https://www.uneca.org'
    },
    {
      name: 'University of Ghana Business School',
      category: 'academic',
      description: 'Research partnership and academic collaboration.',
      website: 'https://ugbs.ug.edu.gh'
    },
    {
      name: 'World Bank Group',
      category: 'strategic',
      description: 'Partnership on development projects and economic research.',
      website: 'https://www.worldbank.org'
    }
  ]

  const categoryColors = {
    strategic: 'bg-ghana-green/10 text-ghana-green',
    academic: 'bg-ghana-yellow/10 text-ghana-yellow',
    corporate: 'bg-ghana-red/10 text-ghana-red',
    government: 'bg-blue-100 text-blue-600'
  }

  return (
    <div className="space-y-16">
      {/* Strategic Partners */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {partners.map((partner, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            viewport={{ once: true }}
            className="bg-white rounded-lg shadow-lg p-6"
          >
            <div className="h-24 flex items-center justify-center mb-6">
              {partner.logo ? (
                <img
                  src={partner.logo}
                  alt={partner.name}
                  className="max-h-full max-w-full object-contain"
                />
              ) : (
                <div className="w-full h-full bg-gradient-ghana opacity-10 rounded" />
              )}
            </div>
            <div>
              <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium mb-4 ${categoryColors[partner.category]}`}>
                {partner.category.charAt(0).toUpperCase() + partner.category.slice(1)}
              </span>
              <h3 className="text-xl font-bold mb-2">{partner.name}</h3>
              <p className="text-gray-600 mb-4">{partner.description}</p>
              {partner.website && (
                <a
                  href={partner.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-ghana-green hover:text-ghana-yellow transition-colors duration-200"
                >
                  Visit Website →
                </a>
              )}
            </div>
          </motion.div>
        ))}
      </div>

      {/* Partnership Benefits */}
      <div className="bg-gray-50 rounded-lg p-8">
        <h3 className="text-2xl font-bold mb-6 text-center">Partnership Benefits</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {[
            {
              title: 'Network Access',
              description: 'Connect with key decision-makers and industry leaders across Africa.'
            },
            {
              title: 'Research Collaboration',
              description: 'Participate in groundbreaking research and policy development.'
            },
            {
              title: 'Event Participation',
              description: 'Priority access and speaking opportunities at major economic forums.'
            }
          ].map((benefit, index) => (
            <div key={index} className="bg-white rounded-lg p-6 shadow-md">
              <h4 className="text-xl font-bold mb-3">{benefit.title}</h4>
              <p className="text-gray-600">{benefit.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Partnership Process */}
      <div className="max-w-4xl mx-auto">
        <h3 className="text-2xl font-bold mb-8 text-center">Become a Partner</h3>
        <div className="space-y-4">
          {[
            {
              step: 1,
              title: 'Initial Consultation',
              description: 'Schedule a meeting to discuss partnership opportunities and alignment.'
            },
            {
              step: 2,
              title: 'Proposal Review',
              description: 'Review detailed partnership proposal and benefits package.'
            },
            {
              step: 3,
              title: 'Agreement Finalization',
              description: 'Finalize partnership terms and sign collaboration agreement.'
            }
          ].map((step, index) => (
            <div key={index} className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-ghana-green text-white rounded-full flex items-center justify-center flex-shrink-0">
                {step.step}
              </div>
              <div>
                <h4 className="text-lg font-bold mb-1">{step.title}</h4>
                <p className="text-gray-600">{step.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
