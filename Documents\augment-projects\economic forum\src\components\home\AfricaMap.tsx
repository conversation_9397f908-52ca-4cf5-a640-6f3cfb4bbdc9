'use client'

import { motion } from 'framer-motion'
import { MapPin, TrendingUp, Users, Building } from 'lucide-react'

const africaRegions = [
  {
    name: 'North Africa',
    countries: ['Egypt', 'Libya', 'Tunisia', 'Algeria', 'Morocco', 'Sudan'],
    stats: { gdp: '$1.2T', growth: '4.2%', population: '250M' },
    color: 'from-ghana-red to-red-600',
    position: { top: '15%', left: '45%' }
  },
  {
    name: 'West Africa',
    countries: ['Nigeria', 'Ghana', 'Senegal', 'Mali', 'Burkina Faso', 'Ivory Coast'],
    stats: { gdp: '$800B', growth: '5.8%', population: '400M' },
    color: 'from-ghana-yellow to-yellow-600',
    position: { top: '40%', left: '25%' }
  },
  {
    name: 'East Africa',
    countries: ['Kenya', 'Ethiopia', 'Tanzania', 'Uganda', 'Rwanda', 'Somalia'],
    stats: { gdp: '$600B', growth: '6.5%', population: '450M' },
    color: 'from-ghana-green to-green-600',
    position: { top: '35%', left: '65%' }
  },
  {
    name: 'Central Africa',
    countries: ['DRC', 'Cameroon', 'Chad', 'CAR', 'Gabon', 'Congo'],
    stats: { gdp: '$400B', growth: '3.8%', population: '180M' },
    color: 'from-blue-500 to-blue-700',
    position: { top: '50%', left: '45%' }
  },
  {
    name: 'Southern Africa',
    countries: ['South Africa', 'Zimbabwe', 'Botswana', 'Zambia', 'Namibia', 'Lesotho'],
    stats: { gdp: '$900B', growth: '4.1%', population: '200M' },
    color: 'from-purple-500 to-purple-700',
    position: { top: '75%', left: '50%' }
  }
]

const keyInitiatives = [
  {
    title: 'AfCFTA Implementation',
    description: 'Supporting the African Continental Free Trade Area across all 54 countries',
    icon: Building,
    impact: '$3.4T Trade Volume',
  },
  {
    title: 'Digital Infrastructure',
    description: 'Expanding broadband connectivity and digital payment systems',
    icon: TrendingUp,
    impact: '500M+ Connected',
  },
  {
    title: 'Youth Employment',
    description: 'Creating opportunities for Africa\'s growing young population',
    icon: Users,
    impact: '10M+ Jobs Created',
  },
]

export default function AfricaMap() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <span className="inline-block px-4 py-2 bg-ghana-yellow/10 text-ghana-red font-semibold rounded-full text-sm mb-4">
            Continental Impact
          </span>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 font-serif">
            Transforming Africa
            <span className="text-ghana-green"> Region by Region</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Our initiatives span across all African regions, fostering economic 
            integration and sustainable development from Cairo to Cape Town.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Interactive Map */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative"
          >
            {/* Simplified Africa Map Container */}
            <div className="relative w-full h-96 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl overflow-hidden">
              {/* Africa Silhouette - Simplified SVG */}
              <svg
                viewBox="0 0 400 500"
                className="absolute inset-0 w-full h-full"
                fill="none"
              >
                {/* Simplified Africa shape */}
                <path
                  d="M200 50 C250 50, 300 80, 320 120 C340 160, 350 200, 340 240 C330 280, 320 320, 310 360 C300 400, 280 440, 250 460 C220 480, 180 480, 150 460 C120 440, 100 400, 90 360 C80 320, 70 280, 60 240 C50 200, 60 160, 80 120 C100 80, 150 50, 200 50 Z"
                  fill="url(#africaGradient)"
                  className="drop-shadow-lg"
                />
                <defs>
                  <linearGradient id="africaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#CE1126" stopOpacity="0.1" />
                    <stop offset="50%" stopColor="#FCD116" stopOpacity="0.1" />
                    <stop offset="100%" stopColor="#007847" stopOpacity="0.1" />
                  </linearGradient>
                </defs>
              </svg>

              {/* Regional Markers */}
              {africaRegions.map((region, index) => (
                <motion.div
                  key={region.name}
                  initial={{ opacity: 0, scale: 0 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.2 }}
                  className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer group"
                  style={{ top: region.position.top, left: region.position.left }}
                >
                  <div className={`w-4 h-4 bg-gradient-to-r ${region.color} rounded-full shadow-lg animate-pulse`}>
                    <div className="absolute inset-0 bg-white rounded-full animate-ping opacity-75"></div>
                  </div>
                  
                  {/* Tooltip */}
                  <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10">
                    <div className="bg-white rounded-lg shadow-xl p-4 min-w-48 border border-gray-200">
                      <h4 className="font-semibold text-gray-900 mb-2">{region.name}</h4>
                      <div className="space-y-1 text-sm text-gray-600">
                        <div className="flex justify-between">
                          <span>GDP:</span>
                          <span className="font-medium">{region.stats.gdp}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Growth:</span>
                          <span className="font-medium text-green-600">{region.stats.growth}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Population:</span>
                          <span className="font-medium">{region.stats.population}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Legend */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              viewport={{ once: true }}
              className="mt-6 bg-gray-50 rounded-xl p-4"
            >
              <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                <MapPin className="w-5 h-5 mr-2 text-ghana-red" />
                Regional Overview
              </h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
                {africaRegions.map((region, index) => (
                  <div key={region.name} className="flex items-center space-x-2">
                    <div className={`w-3 h-3 bg-gradient-to-r ${region.color} rounded-full`}></div>
                    <span className="text-gray-700">{region.name}</span>
                  </div>
                ))}
              </div>
            </motion.div>
          </motion.div>

          {/* Key Initiatives */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div>
              <h3 className="text-3xl font-bold text-gray-900 mb-6 font-serif">
                Key Continental Initiatives
              </h3>
              <p className="text-gray-600 leading-relaxed mb-8">
                Our strategic initiatives are designed to accelerate economic integration 
                and development across all African regions, creating a unified and 
                prosperous continental economy.
              </p>
            </div>

            <div className="space-y-6">
              {keyInitiatives.map((initiative, index) => {
                const Icon = initiative.icon
                return (
                  <motion.div
                    key={initiative.title}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                    viewport={{ once: true }}
                    whileHover={{ x: 5 }}
                    className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-all duration-300 group"
                  >
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-ghana-yellow to-ghana-green rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <Icon className="w-6 h-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-ghana-red transition-colors duration-300">
                          {initiative.title}
                        </h4>
                        <p className="text-gray-600 mb-3 leading-relaxed">
                          {initiative.description}
                        </p>
                        <div className="inline-flex items-center px-3 py-1 bg-ghana-green/10 text-ghana-green rounded-full text-sm font-medium">
                          <TrendingUp className="w-4 h-4 mr-1" />
                          {initiative.impact}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )
              })}
            </div>

            {/* CTA */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              viewport={{ once: true }}
              className="pt-6"
            >
              <button className="w-full bg-gradient-to-r from-ghana-red to-ghana-green text-white font-semibold py-4 rounded-xl hover:shadow-lg transition-all duration-300">
                Explore Our Continental Impact
              </button>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
